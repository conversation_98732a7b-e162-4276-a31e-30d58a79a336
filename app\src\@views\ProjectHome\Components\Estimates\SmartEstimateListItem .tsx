import { Estimate } from 'lib/Models/Estimate';
import React, { useState } from 'react';
import {
    DndContext,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
    DragEndEvent,
    DragOverlay,
    DragStartEvent,
} from '@dnd-kit/core';
import {
    arrayMove,
    SortableContext,
    sortableKeyboardCoordinates,
    verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
    restrictToVerticalAxis,
    restrictToParentElement,
} from '@dnd-kit/modifiers';
import { Box, Grid, IconButton, Typography } from '@mui/material';
import { ReactComponent as DragIcon } from 'assets/DragIcon.svg';
import { ReactComponent as MinusDelete } from 'assets/MinusDelete.svg';
import { ReactComponent as VisibilityIconOff } from 'assets/VisibilityIconOff.svg';
import { ReactComponent as VisibilityIconOn } from 'assets/VisibilityIconOn.svg';
import { ReactComponent as PaletteIcon } from 'assets/PaletteIcon.svg';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { EstimateItem } from 'lib/Models/EstimateItem';
import { USDollar } from 'lib/util/formatters';

interface ISmartEstimateListItem {
    estimate: Estimate;
}

interface ISortableLineItemProps {
    item: EstimateItem;
    isDragging?: boolean;
    index?: number | undefined;
    totalItems?: number | undefined;
}
interface ILineItemDisplayProps {
    item: EstimateItem;
    isDragging?: boolean;
    isOverlay?: boolean;
    dragHandleProps?: any;
    style?: React.CSSProperties;
    index?: number | undefined;
    totalItems?: number | undefined;
}
const SmartEstimateListItem: React.FC<ISmartEstimateListItem> = ({
    estimate,
}) => {
    const [lineItems, setLineItems] = useState<EstimateItem[]>(
        estimate.estimateItems
    );

    const [activeId, setActiveId] = useState<string | null>(null);

    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const handleDragStart = (event: DragStartEvent) => {
        const { active } = event;
        setActiveId(active.id as string);
    };

    const handleDragEnd = (event: DragEndEvent) => {
        const { active, over } = event;

        if (over && active.id !== over.id) {
            const oldIndex = lineItems.findIndex(
                (item) => item.id === active.id
            );
            const newIndex = lineItems.findIndex((item) => item.id === over.id);

            // Only update if position actually changed
            if (oldIndex !== newIndex) {
                setLineItems(arrayMove(lineItems, oldIndex, newIndex));
                console.log(
                    `Moved item from position ${oldIndex} to ${newIndex}`
                );
            }
        }

        setActiveId(null);
    };

    const activeItem = activeId
        ? lineItems.find((item) => item.id === activeId)
        : null;

    return (
        <>
            <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragStart={handleDragStart}
                onDragEnd={handleDragEnd}
                modifiers={[restrictToVerticalAxis, restrictToParentElement]}
            >
                <SortableContext
                    items={lineItems}
                    strategy={verticalListSortingStrategy}
                >
                    {lineItems.map((item, index) => (
                        <SortableLineItem
                            totalItems={lineItems.length}
                            key={item.id}
                            item={item}
                            index={index}
                            isDragging={activeId === item.id}
                        />
                    ))}
                </SortableContext>
                <DragOverlay>
                    {activeItem ? <DragOverlayItem item={activeItem} /> : null}
                </DragOverlay>
            </DndContext>
        </>
    );
};

const SortableLineItem: React.FC<ISortableLineItemProps> = ({
    item,
    isDragging,
    index,
    totalItems,
}) => {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
        isDragging: isSortableDragging,
    } = useSortable({ id: item.id });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging || isSortableDragging ? 0.5 : 1,
    };

    return (
        <LineItemDisplay
            totalItems={totalItems}
            index={index}
            item={item}
            isDragging={isDragging || isSortableDragging}
            dragHandleProps={{ ...attributes, ...listeners }}
            ref={setNodeRef}
            style={style}
        />
    );
};

const DragOverlayItem = ({ item }: { item: EstimateItem }) => {
    return <LineItemDisplay item={item} isOverlay={true} />;
};

const percent = new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
});

const decimal = new Intl.NumberFormat('en-US', {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
});

const getItemProperties = (item: EstimateItem, dragHandleProps = {}) => {
    if (item.itemType === 'Markup' || item.itemType === 'Discount') {
        return `${percent.format(item.total * 0.01)} ${
            item.itemType === 'Markup' ? 'markup' : 'discount'
        }`;
    }
    let unit: any = '';
    switch (item.unitType) {
        case 'Squares':
            unit = `${decimal.format(item.units)} sq.`;
            break;
        case 'SquareFeet':
        case 'LinearFeet':
            unit = `${item.units} ft.`;
            break;
        case 'Each':
            unit = `${item.units} ea.`;
            break;
        default:
            break;
    }
    return (
        <Typography variant='body1' color={'#888888'} mt='0!important'>
            {unit}
        </Typography>
    );
};

const getItemColor = (item: EstimateItem, dragHandleProps = {}) => {
    if (item.color?.description) {
        return (
            <>
                <Typography
                    style={{
                        fontSize: 14,
                        fontWeight: 400,
                        color: '#555',
                        lineHeight: 'normal',
                        paddingLeft: '10px',
                    }}
                >
                    -
                </Typography>
                <IconButton
                    size='small'
                    style={{ paddingRight: '10px' }}
                    {...dragHandleProps}
                >
                    <PaletteIcon />
                </IconButton>
                <Typography
                    style={{
                        fontSize: 14,
                        fontWeight: 400,
                        color: '#056CF2',
                        lineHeight: '20px',
                        letterSpacing: '0.25px',
                        marginTop: '3px',
                    }}
                >
                    {item.color?.description}
                </Typography>
            </>
        );
    }
    return <></>;
};

const LineItemDisplay = React.forwardRef<HTMLDivElement, ILineItemDisplayProps>(
    (
        {
            item,
            isDragging = false,
            isOverlay = false,
            dragHandleProps = {},
            style,
            index,
            totalItems,
        },
        ref
    ) => {
        const getOutlineStyle = () => {
            if (isOverlay) return '1px solid #FFD700';
            if (isDragging) return '1px dashed #FFD700';
            return 'none';
        };

        return (
            <Grid
                container
                direction='row'
                alignItems='center'
                bgcolor='#FFF'
                ref={ref}
                style={style}
                sx={{
                    borderTop: '1px solid #D9D9D9',
                    borderRight: '1px solid #D9D9D9',
                    borderLeft: '1px solid #D9D9D9',
                    paddingTop: '15px',
                    paddingBottom: '15px',
                    outline: getOutlineStyle(),
                    ...(index === 0 && {
                        borderRadius: '4px 4px 0 0',
                    }),
                    ...(index &&
                        totalItems &&
                        index === totalItems - 1 && {
                            borderBottom: '1px solid #D9D9D9',
                            borderRadius: '0 0 4px 4px',
                        }),
                }}
            >
                <Grid item display='flex' xs={1} justifyContent='center'>
                    <IconButton
                        size='small'
                        sx={{
                            paddingLeft: { xs: '15px', md: 0 },
                            paddingRight: { xs: '15px', md: 0 },
                        }}
                        {...dragHandleProps}
                    >
                        <DragIcon />
                    </IconButton>
                </Grid>
                <Grid item xs={11}>
                    <Grid container display={'flex'}>
                        {item.imageUrl && (
                            <Grid
                                item
                                display='flex'
                                xs={3}
                                sm={2}
                                md={1}
                                lg={1}
                                alignItems='center'
                            >
                                <img
                                    src={item.imageUrl}
                                    alt='Roofing'
                                    style={{
                                        marginRight: '10px',

                                        width: '98px',
                                        height: '68px',
                                        backgroundColor:
                                            'rgba(211, 211, 211, 0.5)',
                                        borderRadius: '4px',
                                    }}
                                />
                            </Grid>
                        )}
                        <Grid
                            container
                            item
                            display='flex'
                            xs={item.imageUrl ? 8 : 11}
                            sm={item.imageUrl ? 4 : 6}
                            md={item.imageUrl ? 6 : 7}
                            lg={item.imageUrl ? 6 : 7}
                            direction={'column'}
                            justifyContent='center'
                        >
                            <Grid item>
                                <Typography
                                    fontWeight='500'
                                    color='#000000'
                                    fontSize='16px'
                                    lineHeight='20px'
                                    letterSpacing='0.25px'
                                >
                                    {item.description}
                                </Typography>
                            </Grid>
                            <Grid>
                                <Box display='flex' alignItems='center'>
                                    {getItemProperties(item, dragHandleProps)}
                                    {getItemColor(item, dragHandleProps)}
                                </Box>
                            </Grid>
                        </Grid>
                        <Grid
                            container
                            item
                            sm={5}
                            md={5}
                            lg={5}
                            display='flex'
                            alignItems='center'
                            justifyContent='flex-end'
                            sx={{
                                paddingTop: { xs: '20px', md: 0 },
                                paddingRight: { xs: 0, md: '15px' },
                            }}
                        >
                            <Grid>
                                <Typography
                                    fontFamily='Roboto'
                                    fontSize='16px'
                                    fontStyle='normal'
                                    textAlign='right'
                                    fontWeight='400'
                                    color='#222222'
                                    lineHeight='24px'
                                    letterSpacing='0.5px'
                                    sx={{ marginRight: '20px' }}
                                >
                                    {USDollar.format(item.total)}
                                </Typography>
                            </Grid>
                            <Grid>
                                <IconButton
                                    size='small'
                                    sx={{
                                        marginRight: '5px',
                                        cursor: 'pointer',
                                    }}
                                >
                                    {true ? (
                                        <VisibilityIconOn />
                                    ) : (
                                        <VisibilityIconOff />
                                    )}
                                </IconButton>

                                <IconButton
                                    size='small'
                                    sx={{
                                        cursor: 'pointer',
                                        paddingRight: { xs: '15px', md: 0 },
                                    }}
                                >
                                    <MinusDelete />
                                </IconButton>
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </Grid>
        );
    }
);

export default SmartEstimateListItem;
