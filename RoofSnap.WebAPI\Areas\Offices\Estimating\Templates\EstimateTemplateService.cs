using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using RoofSnap.WebAPI.Areas.Projects;
using RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateOptions;
using RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateItems;
using RoofSnap.WebAPI.Common;
using RoofSnap.WebAPI.Common.Data;
using RoofSnap.WebAPI.Common.WebAPI;
using RoofSnap.WebAPI.Common.Wrappers;

namespace RoofSnap.WebAPI.Areas.Offices.Estimating.Templates
{
    public interface IEstimateTemplateService : IService<DbEstimateTemplate, string>
    {
        Task<DbEstimateTemplate> CreateEstimateTemplateAsync(string id, string estimateId, DbEstimateTemplate dbEstimateTemplate);
        Task<DbEstimateTemplate> UpdateEstimateTemplateFromEstimateAsync(string templateId, string projectId, string estimateId);
        void DeleteEstimateTemplate(DbEstimateTemplate dbEstimateTemplate);
    }

    public class EstimateTemplateService : IEstimateTemplateService
    {
        private readonly IDateTimeWrapper _dateTimeWrapper;
        private readonly IDbEstimateTemplateItemFactory _dbEstimateTemplateItemFactory;
        private readonly IRoofSnapLiveDbContext _dbContext;

        public EstimateTemplateService(IDateTimeWrapper dateTimeWrapper,
            IDbEstimateTemplateItemFactory dbEstimateTemplateItemFactory,
            IRoofSnapLiveDbContext dbContext)
        {
            _dateTimeWrapper = dateTimeWrapper;
            _dbEstimateTemplateItemFactory = dbEstimateTemplateItemFactory;
            _dbContext = dbContext;
        }

        public Task<IList<DbEstimateTemplate>> GetPagedAsync(PagingInfo pagingInfo, string search = "", string[] ids = null)
        {
            return _dbContext.EstimateTemplates
                .OrderByDescending(estimatTemplate => estimatTemplate.CreatedAt)
                .AsPagedAsync(pagingInfo);
        }

        public DbEstimateTemplate Get(string id)
        {
            return _dbContext.EstimateTemplates.Find(id);
        }

        public void DeleteEstimateTemplate(DbEstimateTemplate dbEstimateTemplate)
        {
            _dbContext.EstimateTemplates.Remove(dbEstimateTemplate);
        }

        public async Task<DbEstimateTemplate> CreateEstimateTemplateAsync(string id, string estimateId, DbEstimateTemplate dbEstimateTemplate)
        {
            DbProject dbProject = await _dbContext.Projects.GetByIdOrShortCodeAsync(id);
            if (dbProject == null)
                return null;

            dbEstimateTemplate.OrganizationId = dbProject.OrganizationId;
            DateTime now = _dateTimeWrapper.Now();
            dbEstimateTemplate.UpdatedAt = now;
            //this nonsense is required for iOS compatability
            {
                dbEstimateTemplate.OfficeId = null;
                dbEstimateTemplate.IsActive = null;
                dbEstimateTemplate.CreatedBy = "";
            }

            _dbContext.EstimateTemplates.Add(dbEstimateTemplate);


            DbProjectEstimateOption estimate = await _dbContext.Entry(dbProject)
                .Collection(project => project.ProjectEstimateOptions)
                .Query()
                .FirstOrDefaultAsync(projectEstimateOption => projectEstimateOption.Id == estimateId);

            if (estimate == null)
                throw new EntityNotFoundException<DbProjectEstimateOption>(estimateId);

            if (estimate.ProjectEstimateItems.Any())
            {
                //if an office priced chargeable item has a chargeable item id of 0, it is a custom item and we do not want that in the template
                IEnumerable<DbEstimateTemplateItem> estimateTemplateItems = estimate.ProjectEstimateItems
                    .Where(ei => ei.OfficePricedChargeableItem != null && ei.OfficePricedChargeableItem.ChargeableItemId != 0)
                    .Select(estimateItem => _dbEstimateTemplateItemFactory.Create(dbEstimateTemplate.Id, estimateItem));

                foreach (DbEstimateTemplateItem dbEstimateTemplateItem in estimateTemplateItems)
                {
                    _dbContext.EstimateTemplateItems.Add(dbEstimateTemplateItem);
                }
            }

            return dbEstimateTemplate;
        }

        public async Task<DbEstimateTemplate> UpdateEstimateTemplateFromEstimateAsync(string templateId, string projectId, string estimateId)
        {
            // Get the existing template
            DbEstimateTemplate existingTemplate = Get(templateId);
            if (existingTemplate == null)
                return null;

            // Get the estimate with its items
            DbProjectEstimateOption estimate = await _dbContext.ProjectEstimateOptions
                .Include(e => e.ProjectEstimateItems.Select(ei => ei.OfficePricedChargeableItem))
                .FirstOrDefaultAsync(e => e.Id == estimateId && e.ProjectId == projectId);

            if (estimate == null)
                return null;

            // Update the template's updated timestamp
            DateTime now = _dateTimeWrapper.Now();
            existingTemplate.UpdatedAt = now;

            // Get current template items
            var existingTemplateItems = await _dbContext.EstimateTemplateItems
                .Where(eti => eti.EstimateTemplateId == templateId)
                .ToListAsync();

            // Get estimate items that should be in the template (excluding custom items)
            // Note: Currently using CreatedAt for ordering since DbProjectEstimateItem doesn't have an Order field.
            // This preserves the original order items were added. For drag-and-drop reordering to be preserved,
            // the frontend would need to persist order changes to the backend first.
            var estimateItemsToInclude = estimate.ProjectEstimateItems
                .Where(ei => ei.OfficePricedChargeableItem != null && ei.OfficePricedChargeableItem.ChargeableItemId != 0)
                .OrderBy(ei => ei.CreatedAt) // Use CreatedAt to preserve the order items were added
                .ToList();

            // Create sets for comparison
            var currentChargeableItemIds = existingTemplateItems.Select(eti => eti.ChargeableItemId).ToHashSet();
            var newChargeableItemIds = estimateItemsToInclude.Select(ei => ei.OfficePricedChargeableItemId).ToHashSet();

            // Remove template items that are no longer in the estimate
            var itemsToRemove = existingTemplateItems
                .Where(eti => !newChargeableItemIds.Contains(eti.ChargeableItemId))
                .ToList();

            foreach (var itemToRemove in itemsToRemove)
            {
                // Find the entity by ID to ensure it's properly tracked by Entity Framework
                var trackedItem = _dbContext.EstimateTemplateItems.Find(itemToRemove.Id);
                if (trackedItem != null)
                {
                    _dbContext.EstimateTemplateItems.Remove(trackedItem);
                }
            }

            // Add new template items that don't exist yet and update order for existing ones
            var itemsToAdd = estimateItemsToInclude
                .Where(ei => !currentChargeableItemIds.Contains(ei.OfficePricedChargeableItemId))
                .ToList();

            for (int i = 0; i < estimateItemsToInclude.Count; i++)
            {
                var estimateItem = estimateItemsToInclude[i];

                if (itemsToAdd.Contains(estimateItem))
                {
                    // Add new template item
                    var templateItem = new DbEstimateTemplateItem
                    {
                        EstimateTemplateId = templateId,
                        ChargeableItemId = estimateItem.OfficePricedChargeableItemId,
                        Order = i + 1, // Set explicit order starting from 1
                        ItemType = estimateItem.ItemType,
                        UpdatedAt = now
                    };
                    _dbContext.EstimateTemplateItems.Add(templateItem);
                }
                else
                {
                    // Update existing template item order
                    var existingItem = existingTemplateItems
                        .FirstOrDefault(eti => eti.ChargeableItemId == estimateItem.OfficePricedChargeableItemId);

                    if (existingItem != null)
                    {
                        existingItem.Order = i + 1;
                        existingItem.UpdatedAt = now;
                    }
                }
            }

            return existingTemplate;
        }
    }
}