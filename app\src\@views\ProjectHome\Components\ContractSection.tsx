import DocumentTemplateCategories from '../../../lib/DocumentTemplateCategories';
import { Box, Grid, IconButton, Tooltip } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { Typography } from '@ui';
import LockIcon from '@material-ui/icons/Lock';
import SectionTitle from './SectionTitle';
import { Project, RoofSnapState } from 'lib/Models';
import { fromIsoUTCDateToString } from 'lib/util/formatters';
import { useDispatch, useSelector } from 'react-redux';
import { DateTime } from 'luxon';
import { ReactComponent as EditIcon } from 'assets/EditIconBlack.svg';
import NonSubscriberModal from './NonSubscriberModal';
import { getDocuments } from 'actions/documentsActions';
import LoggerWrapper from 'lib/Logger';
import { Estimate, EstimateType } from 'lib/Models/Estimate';
import NoteAddIcon from '@material-ui/icons/NoteAddOutlined';
import { LinkContainer } from '@ui/LinkContainer';
import ContractEditorModal from './ContractEditorModal';
import ContractAddModal from './ContractAddModal';

type ContractSectionProps = {
    project: Project;
    estimates: Estimate[] | undefined;
};

interface IContractData {
    name: string | null;
    createdAt: DateTime;
}

const ContractSection = ({ project, estimates }: ContractSectionProps) => {
    const dispatch = useDispatch();

    const { organization } = useSelector((state: RoofSnapState) => state);

    const [dialogOpen, setDialogOpen] = useState<boolean>(false);
    const [addContractDialogOpen, setAddContractDialogOpen] =
        useState<boolean>(false);

    const isSubscriber = !organization.subscriptionExpired;

    const [contractData, setContractData] = useState<IContractData | undefined>(
        undefined
    );

    const hasEstimates =
        estimates !== undefined &&
        estimates?.filter((obj) => obj.estimateType === EstimateType.Itemized)
            .length > 0;

    const hasContract = typeof contractData !== 'undefined';

    const fetchContractTerms = async () => {
        try {
            const response: any = await dispatch(getDocuments(project.id));

            const contract = response.documents.findLast(
                (doc: any) =>
                    doc?.template?.templateCategoryId ===
                    DocumentTemplateCategories.Contract
            );
            setContractData(contract);
        } catch (error) {
            LoggerWrapper.log(error);
        }
    };

    useEffect(() => {
        fetchContractTerms();

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [project.id]);

    const handleCancelDialog = () => {
        setDialogOpen(false);
    };

    const handleAddContractClick = () => {
        if (!hasEstimates) {
            return false;
        }

        if (!isSubscriber) {
            setDialogOpen(true);
            return false;
        }

        setAddContractDialogOpen(true);
    };

    const DisplayName = () => (
        <Grid item md={4} xs={10} order={1}>
            <Typography
                sx={{
                    letterSpacing: '0.5px',
                    color: '#222',
                }}
            >
                Contract Terms
            </Typography>
        </Grid>
    );
    const DisplayDate = () => (
        <Grid item md={4} xs={12} order={{ xs: 3, md: 2 }}>
            <Typography
                sx={{
                    mt: { xs: '1rem', md: 0 },
                    letterSpacing: '0.25px',
                    color: '#555',
                    fontSize: '0.875rem',
                }}
            >
                {hasContract &&
                    fromIsoUTCDateToString(contractData.createdAt.toString())}
            </Typography>
        </Grid>
    );

    const AddContract = () => (
        <Box
            sx={{
                borderRadius: '0.75rem',
                border: '1px solid #d9d9d9',
                background: '#fff',
                alignItems: 'center',
            }}
        >
            <Box justifyContent='center' display='flex' width={'100%'}>
                <LinkContainer
                    style={{
                        width: '100%',
                        padding: '1rem',
                    }}
                    onClick={handleAddContractClick}
                >
                    <IconButton disabled={!hasEstimates}>
                        <NoteAddIcon
                            htmlColor={hasEstimates ? '#056CF2' : ''}
                        />
                    </IconButton>
                    <Typography
                        variant='link'
                        style={!hasEstimates ? { color: '#BCBCBC' } : {}}
                    >
                        Add Contract
                    </Typography>
                </LinkContainer>
            </Box>
        </Box>
    );

    const DisplayActionIcon = () => (
        <Grid
            item
            md={4}
            xs={2}
            order={{ xs: 1, md: 3 }}
            sx={{ textAlign: 'right' }}
        >
            <IconButton onClick={() => setDialogOpen(true)}>
                <EditIcon />
            </IconButton>
        </Grid>
    );

    return (
        <Box mt='30px'>
            <Box sx={{ display: 'flex', gap: '5px' }}>
                <SectionTitle title='Contract' />
                {!isSubscriber && (
                    <LockIcon
                        style={{ height: 22, width: 20, color: '#878787' }}
                    />
                )}
            </Box>
            <Typography
                mb={1}
                sx={{
                    color: '#555',
                    fontSize: '0.875rem',
                    letterSpacing: '0.25px',
                }}
            >
                These are your legal terms. Insert images, page breaks, text
                formatting and tokens, such as signature blocks, initials.
            </Typography>
            {!hasEstimates ? (
                <Tooltip title='Add an estimate to add a contract.'>
                    <div>
                        <AddContract />
                    </div>
                </Tooltip>
            ) : !hasContract ? (
                <AddContract />
            ) : (
                <Grid
                    sx={{
                        display: 'flex',
                        flexWrap: 'wrap',
                        padding: '1rem',
                        borderRadius: '0.75rem',
                        border: '1px solid #d9d9d9',
                        background: '#fff',
                        alignItems: 'center',
                    }}
                    item
                    xs={12}
                >
                    <DisplayName />
                    <DisplayDate />
                    <DisplayActionIcon />
                </Grid>
            )}

            <NonSubscriberModal
                open={!isSubscriber && dialogOpen}
                onCancelClick={handleCancelDialog}
            />
            <ContractEditorModal
                open={!!(isSubscriber && dialogOpen)}
                handleCancel={handleCancelDialog}
                organizationId={project.organizationId}
                officeId={project.officeId}
                contractData={contractData}
                setContractData={setContractData}
            />
            <ContractAddModal
                open={addContractDialogOpen}
                setOpen={setAddContractDialogOpen}
                onAdd={fetchContractTerms}
            />
        </Box>
    );
};

export default ContractSection;
