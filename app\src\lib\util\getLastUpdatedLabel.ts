export function getLastUpdatedLabel(updatedTime: Date | string | number): string {
    const updated = new Date(updatedTime);
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - updated.getTime()) / 60000);

    if (diffMinutes < 1) {
        return "Updated just now";
    }

    if (diffMinutes < 60) {
        return "Updated 5 minutes ago";
    }

    const pad = (n: number) => String(n).padStart(2, '0');

    const formattedDate = `${pad(updated.getMonth() + 1)}/${pad(updated.getDate())}/${updated.getFullYear()}`;
    const formattedTime = `${pad(updated.getHours())}:${pad(updated.getMinutes())}`;

    return `Updated ${formattedDate} at ${formattedTime}`;
}
